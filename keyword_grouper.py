#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SEM关键词自动分组工具
基于CSV数据进行关键词智能分组
"""

import pandas as pd
import re
from collections import defaultdict
from typing import Dict, List, Tuple
import json

class KeywordGrouper:
    def __init__(self):
        # 软件名称映射表（处理各种变体和拼写错误）
        self.software_mapping = {
            'quicktime': 'QuickTime',
            'qtime': 'QuickTime',
            'quicktimen': 'QuickTime',
            'hwxx': '华语星空',
            '华语星空': '华语星空', 
            '华语拼音输入法': '华语拼音输入法',
            'huayu': '华语拼音输入法',
            '火星文输入法': '火星文输入法',
            'huoxingwen': '火星文输入法',
            'qbittorrent': 'qBittorrent',
            'qbitorrent': 'qBittorrent',
            'utorrent': 'uTorrent',
            'utorent': 'uTorrent',
            'idm': 'IDM',
            'internet download manager': 'IDM',
            'internet download maneger': 'IDM',
            'internet downlod manager': 'IDM',
            'idmnager': 'IDM',
            'fdm': 'Free Download Manager',
            'free download manager': 'Free Download Manager',
            'free downlad manager': 'Free Download Manager',
            'btjl': '百度网盘',
            '百度网盘': '百度网盘',
            'tuotu': '拖拖',
            '拖拖': '拖拖',
            'tuo tu': '拖拖',
            'raysource': 'raysource',
            'raysurce': 'raysource',
            '好用便签': '好用便签',
            'haoyongbianqian': '好用便签',
            'putty': 'PuTTY',
            'puty': 'PuTTY',
            'honeyview': 'Honeyview',
            'honyview': 'Honeyview',
            'honey view': 'Honeyview',
            'inkscape': 'Inkscape',
            'inkscap': 'Inkscape',
            'faststone image viewer': 'FastStone Image Viewer',
            'faststone viewer': 'FastStone Image Viewer',
            'fastston': 'FastStone Image Viewer'
        }
        
        # 意图词分类规则
        self.intent_patterns = {
            '品牌词': [
                r'^([^,]+)$',  # 纯软件名
            ],
            '下载词': [
                r'下载|xiazai|download',
                r'电脑版|pc版|dian nao ban|pcban',
                r'安装包|anzhuangbao'
            ],
            '版本词': [
                r'最新版|新版|官方版|绿色版',
                r'官方下载|官方软件',
                r'正版|免费版'
            ],
            '系统兼容词': [
                r'win7|win10|win11|windows7版|windows10版|windows11版',
                r'windows7|windows10|windows11'
            ],
            '功能词': [
                r'客户端|播放器|输入法|下载工具|图像查看',
                r'管理器|编辑器|浏览器'
            ]
        }
        
        # 软件分类
        self.software_categories = {
            '媒体播放工具': ['QuickTime'],
            '输入法工具': ['华语星空', '华语拼音输入法', '火星文输入法'],
            'BT下载工具': ['qBittorrent', 'uTorrent', '百度网盘'],
            'HTTP下载工具': ['IDM', 'Free Download Manager'],
            '系统工具': ['PuTTY', '好用便签', '拖拖', 'raysource'],
            '图像处理工具': ['Honeyview', 'Inkscape', 'FastStone Image Viewer']
        }

    def load_csv(self, filepath: str) -> pd.DataFrame:
        """加载CSV文件"""
        try:
            # 尝试不同编码
            for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
                try:
                    df = pd.read_csv(filepath, encoding=encoding, header=None)
                    df.columns = ['software', 'keyword']
                    return df
                except UnicodeDecodeError:
                    continue
            raise Exception("无法读取CSV文件，请检查编码格式")
        except Exception as e:
            print(f"读取文件错误: {e}")
            return pd.DataFrame()

    def extract_software_name(self, keyword: str) -> str:
        """提取软件名称"""
        keyword_lower = keyword.lower().strip()
        
        # 直接匹配映射表
        for pattern, software in self.software_mapping.items():
            if pattern in keyword_lower:
                return software
                
        # 如果没找到，返回原始关键词的第一部分
        return keyword.split(',')[0] if ',' in keyword else keyword

    def classify_intent(self, keyword: str) -> str:
        """分类关键词意图"""
        keyword_lower = keyword.lower()
        
        # 按优先级匹配
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, keyword_lower):
                    return intent
        
        return '其他词'

    def get_software_category(self, software: str) -> str:
        """获取软件分类"""
        for category, softwares in self.software_categories.items():
            if software in softwares:
                return category
        return '其他软件'

    def group_keywords(self, df: pd.DataFrame) -> Dict:
        """执行关键词分组"""
        results = {
            'by_software': defaultdict(list),
            'by_intent': defaultdict(list), 
            'by_category': defaultdict(list),
            'statistics': {}
        }
        
        for _, row in df.iterrows():
            keyword = row['keyword']
            
            # 提取软件名称和意图
            software = self.extract_software_name(keyword)
            intent = self.classify_intent(keyword)
            category = self.get_software_category(software)
            
            # 创建关键词对象
            keyword_obj = {
                'keyword': keyword,
                'software': software,
                'intent': intent,
                'category': category
            }
            
            # 分组存储
            results['by_software'][software].append(keyword_obj)
            results['by_intent'][intent].append(keyword_obj)
            results['by_category'][category].append(keyword_obj)
        
        # 统计信息
        results['statistics'] = {
            'total_keywords': len(df),
            'software_count': len(results['by_software']),
            'intent_count': len(results['by_intent']),
            'category_count': len(results['by_category'])
        }
        
        return results

    def export_to_excel(self, results: Dict, output_file: str):
        """导出到Excel"""
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            
            # 按软件分组
            software_data = []
            for software, keywords in results['by_software'].items():
                for kw in keywords:
                    software_data.append({
                        '软件名称': software,
                        '关键词': kw['keyword'],
                        '意图分类': kw['intent'],
                        '软件分类': kw['category']
                    })
            pd.DataFrame(software_data).to_excel(writer, sheet_name='按软件分组', index=False)
            
            # 按意图分组 
            intent_data = []
            for intent, keywords in results['by_intent'].items():
                for kw in keywords:
                    intent_data.append({
                        '意图分类': intent,
                        '关键词': kw['keyword'],
                        '软件名称': kw['software'],
                        '软件分类': kw['category']
                    })
            pd.DataFrame(intent_data).to_excel(writer, sheet_name='按意图分组', index=False)
            
            # 按分类分组
            category_data = []
            for category, keywords in results['by_category'].items():
                for kw in keywords:
                    category_data.append({
                        '软件分类': category,
                        '软件名称': kw['software'],
                        '关键词': kw['keyword'],
                        '意图分类': kw['intent']
                    })
            pd.DataFrame(category_data).to_excel(writer, sheet_name='按分类分组', index=False)
            
            # 统计信息
            stats_data = [
                {'指标': '总关键词数', '数值': results['statistics']['total_keywords']},
                {'指标': '软件数量', '数值': results['statistics']['software_count']},
                {'指标': '意图类型数', '数值': results['statistics']['intent_count']},
                {'指标': '软件分类数', '数值': results['statistics']['category_count']}
            ]
            pd.DataFrame(stats_data).to_excel(writer, sheet_name='统计信息', index=False)

    def export_to_json(self, results: Dict, output_file: str):
        """导出到JSON"""
        # 转换为可序列化格式
        json_results = {}
        for key, value in results.items():
            if isinstance(value, defaultdict):
                json_results[key] = dict(value)
            else:
                json_results[key] = value
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(json_results, f, ensure_ascii=False, indent=2)

    def print_summary(self, results: Dict):
        """打印分组摘要"""
        print("\n" + "="*50)
        print("关键词分组结果摘要")
        print("="*50)
        
        stats = results['statistics']
        print(f"总关键词数: {stats['total_keywords']}")
        print(f"识别软件数: {stats['software_count']}")
        print(f"意图类型数: {stats['intent_count']}")
        print(f"软件分类数: {stats['category_count']}")
        
        print("\n软件分组TOP10:")
        software_sorted = sorted(results['by_software'].items(), 
                               key=lambda x: len(x[1]), reverse=True)
        for software, keywords in software_sorted[:10]:
            print(f"  {software}: {len(keywords)}个关键词")
        
        print("\n意图分布:")
        for intent, keywords in results['by_intent'].items():
            print(f"  {intent}: {len(keywords)}个关键词")
            
        print("\n分类分布:")
        for category, keywords in results['by_category'].items():
            print(f"  {category}: {len(keywords)}个关键词")

def main():
    """主程序入口"""
    # 初始化分组器
    grouper = KeywordGrouper()
    
    # 读取CSV文件
    print("正在读取CSV文件...")
    df = grouper.load_csv('1.csv')
    
    if df.empty:
        print("CSV文件读取失败或为空")
        return
    
    print(f"成功读取 {len(df)} 条关键词数据")
    
    # 执行分组
    print("正在执行关键词分组...")
    results = grouper.group_keywords(df)
    
    # 输出结果
    grouper.print_summary(results)
    
    # 导出文件
    print("\n正在导出结果文件...")
    grouper.export_to_excel(results, 'keyword_groups.xlsx')
    grouper.export_to_json(results, 'keyword_groups.json')
    
    print("\n分组完成！输出文件:")
    print("  - keyword_groups.xlsx (Excel格式)")
    print("  - keyword_groups.json (JSON格式)")

if __name__ == "__main__":
    main()